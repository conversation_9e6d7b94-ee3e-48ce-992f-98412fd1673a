# New Navigation System for Telegram Bot

## Overview
The bot now supports a hierarchical navigation system for series stored in Excel files within a `series` folder.

## Navigation Flow

### 1. Main Categories (Excel Files)
- When user clicks "🎬 المسلسلات", the bot shows all Excel files in the `series` folder
- Each Excel file represents a main category (e.g., "مسلسلات عربية", "أنمي ياباني")

### 2. Sub-Categories (Excel Sheets)
- When user selects an Excel file, the bot shows all sheets within that file
- Each sheet represents a sub-category (e.g., "دراما", "كوميديا", "شونين", "أكشن")

### 3. Series Names
- When user selects a sheet, the bot shows all unique values from the "name" column
- Each name represents a series (e.g., "مسلسل الأطفال", "ناروتو", "ون بيس")

### 4. Seasons
- When user selects a series, the bot shows all available seasons for that series
- Seasons are extracted from the "Season" column

### 5. Episodes (with Pagination)
- When user selects a season, the bot shows all episodes for that season
- Episodes are paginated (15 episodes per page) with "السابق" and "التالي" buttons
- Episodes are extracted from the "Episode" column

### 6. Episode Playback
- When user selects an episode, the bot forwards the content from the specified Telegram channel
- Uses the "Link" and "Channel ID" columns from the Excel file

## Excel File Structure

Each Excel file in the `series` folder should have the following columns:
- `name`: Series name
- `Season`: Season number
- `Episode`: Episode number
- `Link`: Telegram link to the episode
- `Channel ID`: Telegram channel ID (optional, extracted from link if not provided)

## Back Navigation

At each level, users can go back to the previous level:
- Episodes → Seasons: "🔙 رجوع للمواسم"
- Seasons → Series: "🔙 رجوع للمسلسلات"
- Series → Sub-categories: "🔙 رجوع للفئات الفرعية"
- Sub-categories → Main categories: "🔙 رجوع للفئات الرئيسية"
- Main categories → Main menu: "🔙 القائمة الرئيسية"

## Sample Files Created

Three sample Excel files have been created in the `series` folder:

1. **مسلسلات_عربية.xlsx**
   - Sheets: "دراما", "كوميديا"
   - Series: "مسلسل الأطفال", "مسلسل الكبار"

2. **أنمي_ياباني.xlsx**
   - Sheets: "شونين", "أكشن"
   - Series: "ناروتو", "ون بيس", "أتاك أون تايتان", "ديث نوت"

3. **مسلسلات_طويلة.xlsx**
   - Sheet: "مسلسلات مطولة"
   - Series: "مسلسل طويل" (44 episodes across 2 seasons - for testing pagination)

## Key Features

1. **User Activation Check**: Only activated users can access series content
2. **Pagination**: Episodes are paginated to handle large numbers of episodes
3. **Error Handling**: Proper error messages for missing files, sheets, or data
4. **Admin Panel**: Admins can activate users and upload new Excel files
5. **Database Integration**: SQLite database tracks user activation status

## Admin Functions

- `/admin` - Access admin panel (only for users in ADMIN_IDS list)
- "تفعيل مستخدم" - Activate a user by their User ID
- "رفع ملف Excel" - Upload new Excel files to replace existing data

## Usage Instructions

1. Place Excel files in the `series` folder
2. Ensure each Excel file has the required columns
3. Users must be activated by an admin to access content
4. Navigation is intuitive with back buttons at each level
5. Episodes are automatically paginated for better user experience
