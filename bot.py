import telebot
from telebot import types
import pandas as pd
import sqlite3
import os

# 🔥 Your bot token here 🔥
BOT_TOKEN = '7552566588:AAE-25zbCctH9_OyT7wuuZGVd_cVvvstUwU'
bot = telebot.TeleBot(BOT_TOKEN)

# Admin user IDs - add your admin user IDs here
ADMIN_IDS = [5626152805, 1145204670]

# Database initialization
def init_db():
    conn = sqlite3.connect('Database.db')
    cursor = conn.cursor()
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS Users (
        userid INTEGER PRIMARY KEY,
        status INTEGER DEFAULT 0
    )
    ''')
    conn.commit()
    conn.close()

# Initialize database
init_db()

# Load Excel data
excel_file = 'Data.xlsx'
xls = pd.ExcelFile(excel_file)

# Helper to get seasons from a series
def get_seasons(series_name):
    df = pd.read_excel(excel_file, sheet_name=series_name)
    return sorted(df['Season'].dropna().unique())

# Helper to get episodes from a series and season
def get_episodes(series_name, season):
    df = pd.read_excel(excel_file, sheet_name=series_name)
    season_df = df[df['Season'] == season]
    return season_df[['Episode', 'Link']].dropna(subset=['Episode'])

# Helper to create a keyboard with 3 buttons per row and an optional Back button
def create_keyboard(buttons, back_data=None, back_text="🔙 رجوع"):
    markup = types.InlineKeyboardMarkup(row_width=3)
    btn_objs = []
    for btn_text, btn_data in buttons:
        btn_objs.append(types.InlineKeyboardButton(btn_text, callback_data=btn_data))

    # Group buttons into rows of 3
    for i in range(0, len(btn_objs), 3):
        markup.row(*btn_objs[i:i + 3])

    # Add back button at the end if needed
    if back_data:
        markup.row(types.InlineKeyboardButton(back_text, callback_data=back_data))

    return markup

# Helper function to check if user is in database
def check_user_in_db(user_id):
    conn = sqlite3.connect('Database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT status FROM Users WHERE userid = ?', (user_id,))
    result = cursor.fetchone()
    conn.close()
    return result

# Helper function to add user to database
def add_user_to_db(user_id, status=0):
    conn = sqlite3.connect('Database.db')
    cursor = conn.cursor()
    cursor.execute('INSERT OR IGNORE INTO Users (userid, status) VALUES (?, ?)', (user_id, status))
    conn.commit()
    conn.close()

# Helper function to update user status
def update_user_status(user_id, status):
    conn = sqlite3.connect('Database.db')
    cursor = conn.cursor()
    cursor.execute('UPDATE Users SET status = ? WHERE userid = ?', (status, user_id))
    conn.commit()
    conn.close()

# Start command
@bot.message_handler(commands=['start'])
def start(message):
    user_id = message.from_user.id

    # Check if user exists in database
    user_status = check_user_in_db(user_id)

    # If user not in database, add them
    if user_status is None:
        add_user_to_db(user_id)

    markup = types.InlineKeyboardMarkup(row_width=2)
    btn1 = types.InlineKeyboardButton("💬 الدعم", url="https://t.me/Amin_Muhammad")
    btn2 = types.InlineKeyboardButton("🎬 المسلسلات", callback_data="series")
    markup.add(btn1, btn2)

    # Show activation status
    user_status = check_user_in_db(user_id)
    status_text = "حالة الحساب: مفعل ✅" if user_status and user_status[0] == 1 else "حالة الحساب: غير مفعل ❌"
    welcome_text = f"مرحباً بك! اختر خياراً:\n\n{status_text}"

    bot.send_message(message.chat.id, welcome_text, reply_markup=markup)

# Admin command handler
@bot.message_handler(commands=['admin'])
def admin_panel(message):
    user_id = message.from_user.id
    if user_id not in ADMIN_IDS:
        return

    markup = types.InlineKeyboardMarkup(row_width=2)
    btn1 = types.InlineKeyboardButton("تفعيل مستخدم", callback_data="admin_activate")
    btn2 = types.InlineKeyboardButton("رفع ملف Excel", callback_data="admin_excel")
    markup.add(btn1, btn2)
    bot.send_message(message.chat.id, "لوحة التحكم:", reply_markup=markup)

# Dictionary to store user states
user_states = {}

# Callback handler
@bot.callback_query_handler(func=lambda call: True)
def callback_query(call):
    # Show all series
    if call.data == "series":
        # Check if user is activated
        user_id = call.from_user.id
        user_status = check_user_in_db(user_id)

        # If user not in database, add them
        if user_status is None:
            add_user_to_db(user_id)
            user_status = (0,)

        # If user is not activated, show message
        if user_status[0] == 0:
            bot.answer_callback_query(call.id, "حسابك غير مفعل. يرجى التواصل مع الإدارة للتفعيل.", show_alert=True)
            return

        buttons = [(sheet, f"S_{sheet}") for sheet in xls.sheet_names]
        markup = create_keyboard(buttons, back_data="main", back_text="🔙 القائمة الرئيسية")
        bot.edit_message_text("اختر المسلسل:", call.message.chat.id, call.message.message_id, reply_markup=markup)

    # Show seasons for the selected series
    elif call.data.startswith("S_"):
        series_name = call.data.split("_", 1)[1]
        seasons = get_seasons(series_name)
        buttons = [(f"موسم {season}", f"season_{series_name}_{season}") for season in seasons]
        markup = create_keyboard(buttons, back_data="series", back_text="🔙 رجوع للمسلسلات")
        bot.edit_message_text(f"اختر الموسم لـ {series_name}:", call.message.chat.id, call.message.message_id, reply_markup=markup)

    # Show episodes for the selected season
    elif call.data.startswith("season_"):
        _, series_name, season = call.data.split("_", 2)
        season = int(season)
        episodes = get_episodes(series_name, season)
        buttons = [(f"حلقة {row['Episode']}", f"episode_{series_name}_{season}_{int(row['Episode'])}") for _, row in episodes.iterrows()]
        markup = create_keyboard(buttons, back_data=f"S_{series_name}", back_text="🔙 رجوع للمواسم")
        bot.edit_message_text(f"اختر الحلقة من {series_name} موسم {season}:", call.message.chat.id, call.message.message_id, reply_markup=markup)

    # Show episodes for the selected season
    elif call.data.startswith("episode_"):
        _, series_name, season, episode = call.data.split("_", 3)
        season = int(season)
        episode = int(episode)

        df = pd.read_excel(excel_file, sheet_name=series_name)
        row = df[(df['Season'] == season) & (df['Episode'] == episode)].iloc[0]
        link = row['Link']

        # 🔥 NEW: Get from_chat_id from Excel
        from_chat_id_excel = row['Channel ID'] if 'Channel ID' in row and not pd.isna(row['Channel ID']) else None

        if "t.me/c/" in str(link):
            parts = link.split("/")
            message_id = int(parts[5])

            # If from_chat_id found in Excel, use it, else extract from link
            if from_chat_id_excel:
                from_chat_id = int(from_chat_id_excel)
            else:
                chat_numeric_id = parts[4]
                from_chat_id = int(f"-100{chat_numeric_id}")

            print(f"Forwarding message from chat_id: {from_chat_id}, message_id: {message_id}")
            bot.forward_message(chat_id=call.message.chat.id, from_chat_id=from_chat_id, message_id=message_id)

    # Admin: Activate user
    elif call.data == "admin_activate":
        user_id = call.from_user.id
        if user_id not in ADMIN_IDS:
            return

        bot.edit_message_text("أدخل معرف المستخدم (User ID) الذي تريد تفعيله:", call.message.chat.id, call.message.message_id)
        user_states[user_id] = "waiting_for_userid"

    # Admin: Upload Excel
    elif call.data == "admin_excel":
        user_id = call.from_user.id
        if user_id not in ADMIN_IDS:
            return

        bot.edit_message_text("أرسل ملف Excel الجديد:", call.message.chat.id, call.message.message_id)
        user_states[user_id] = "waiting_for_excel"

    # Go back to main menu
    elif call.data == "main":
        start(call.message)

# Message handler for text messages (for admin functions)
@bot.message_handler(func=lambda message: message.from_user.id in user_states, content_types=['text'])
def handle_text_input(message):
    user_id = message.from_user.id
    state = user_states.get(user_id)

    # Admin is waiting for user ID to activate
    if state == "waiting_for_userid":
        try:
            target_user_id = int(message.text.strip())
            # Check if user exists in database
            user_status = check_user_in_db(target_user_id)

            if user_status is None:
                # User not in database, add them
                add_user_to_db(target_user_id, 1)
                bot.reply_to(message, f"تم إضافة المستخدم {target_user_id} وتفعيل حسابه.")
            else:
                # Update user status
                update_user_status(target_user_id, 1)
                bot.reply_to(message, f"تم تفعيل حساب المستخدم {target_user_id}.")

        except ValueError:
            bot.reply_to(message, "الرجاء إدخال رقم صحيح.")

        # Clear user state
        del user_states[user_id]

# Message handler for document uploads (Excel files)
@bot.message_handler(func=lambda message: message.from_user.id in user_states, content_types=['document'])
def handle_document(message):
    user_id = message.from_user.id
    state = user_states.get(user_id)

    # Admin is waiting for Excel file
    if state == "waiting_for_excel":
        # Check if document is an Excel file
        if message.document.mime_type in ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']:
            try:
                # Download file
                file_info = bot.get_file(message.document.file_id)
                downloaded_file = bot.download_file(file_info.file_path)

                # Save file
                with open('Data.xlsx', 'wb') as new_file:
                    new_file.write(downloaded_file)

                # Reload Excel data
                global xls
                xls = pd.ExcelFile('Data.xlsx')

                bot.reply_to(message, "تم استلام وحفظ ملف Excel بنجاح.")
            except Exception as e:
                bot.reply_to(message, f"حدث خطأ أثناء معالجة الملف: {str(e)}")
        else:
            bot.reply_to(message, "الرجاء إرسال ملف Excel فقط.")

        # Clear user state
        del user_states[user_id]

# Polling
bot.infinity_polling()
