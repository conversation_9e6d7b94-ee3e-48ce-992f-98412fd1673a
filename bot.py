import telebot
from telebot import types
import pandas as pd
import sqlite3
import os

# 🔥 Your bot token here 🔥
BOT_TOKEN = '7836004518:AAElj1_pCPQBbFBXC70VzHnJpwDH4hNqimg'
bot = telebot.TeleBot(BOT_TOKEN)

# Admin user IDs - add your admin user IDs here
ADMIN_IDS = [5626152805, 1145204670]

# Database initialization
def init_db():
    conn = sqlite3.connect('Database.db')
    cursor = conn.cursor()
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS Users (
        userid INTEGER PRIMARY KEY,
        status INTEGER DEFAULT 0
    )
    ''')
    conn.commit()
    conn.close()

# Initialize database
init_db()

# Helper functions for new series structure
def get_excel_files_in_series():
    """Get all Excel files in the series folder"""
    series_folder = 'series'
    if not os.path.exists(series_folder):
        os.makedirs(series_folder)
    excel_files = []
    for file in os.listdir(series_folder):
        if file.endswith(('.xlsx', '.xls')):
            excel_files.append(file)
    return excel_files

def get_sheets_from_excel(excel_file):
    """Get all sheet names from an Excel file"""
    try:
        file_path = os.path.join('series', excel_file)
        xls = pd.ExcelFile(file_path)
        return xls.sheet_names
    except Exception as e:
        print(f"Error reading Excel file {excel_file}: {e}")
        return []

def get_series_names_from_sheet(excel_file, sheet_name):
    """Get unique series names from the 'name' column in a sheet"""
    try:
        file_path = os.path.join('series', excel_file)
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        if 'Name' in df.columns:
            return sorted(df['Name'].dropna().unique().tolist())
        else:
            return []
    except Exception as e:
        print(f"Error reading sheet {sheet_name} from {excel_file}: {e}")
        return []

def get_seasons_from_series(excel_file, sheet_name, series_name):
    """Get seasons for a specific series"""
    try:
        file_path = os.path.join('series', excel_file)
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        if 'Name' in df.columns and 'Season' in df.columns:
            series_df = df[df['Name'] == series_name]
            return sorted(series_df['Season'].dropna().unique().tolist())
        else:
            return []
    except Exception as e:
        print(f"Error getting seasons for {series_name}: {e}")
        return []

def get_episodes_from_season(excel_file, sheet_name, series_name, season):
    """Get episodes for a specific series and season"""
    try:
        file_path = os.path.join('series', excel_file)
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        if all(col in df.columns for col in ['Name', 'Season', 'Episode']):
            episodes_df = df[(df['Name'] == series_name) & (df['Season'] == season)]
            return episodes_df[['Episode', 'Link']].dropna(subset=['Episode'])
        else:
            return pd.DataFrame()
    except Exception as e:
        print(f"Error getting episodes for {series_name} season {season}: {e}")
        return pd.DataFrame()

# Helper to create a keyboard with 3 buttons per row and an optional Back button
def create_keyboard(buttons, back_data=None, back_text="🔙 رجوع"):
    markup = types.InlineKeyboardMarkup(row_width=3)
    btn_objs = []
    for btn_text, btn_data in buttons:
        btn_objs.append(types.InlineKeyboardButton(btn_text, callback_data=btn_data))

    # Group buttons into rows of 3
    for i in range(0, len(btn_objs), 3):
        markup.row(*btn_objs[i:i + 3])

    # Add back button at the end if needed
    if back_data:
        markup.row(types.InlineKeyboardButton(back_text, callback_data=back_data))

    return markup

# Helper to create paginated keyboard for episodes
def create_paginated_keyboard(buttons, page=0, items_per_page=15, prefix="", back_data=None, back_text="🔙 رجوع"):
    markup = types.InlineKeyboardMarkup(row_width=3)

    # Calculate pagination
    start_idx = page * items_per_page
    end_idx = start_idx + items_per_page
    page_buttons = buttons[start_idx:end_idx]

    # Add page buttons
    btn_objs = []
    for btn_text, btn_data in page_buttons:
        btn_objs.append(types.InlineKeyboardButton(btn_text, callback_data=btn_data))

    # Group buttons into rows of 3
    for i in range(0, len(btn_objs), 3):
        markup.row(*btn_objs[i:i + 3])

    # Add navigation buttons if needed
    nav_buttons = []
    if page > 0:
        nav_buttons.append(types.InlineKeyboardButton("⬅️ السابق", callback_data=f"{prefix}_prev_{page-1}"))
    if end_idx < len(buttons):
        nav_buttons.append(types.InlineKeyboardButton("التالي ➡️", callback_data=f"{prefix}_next_{page+1}"))

    if nav_buttons:
        markup.row(*nav_buttons)

    # Add back button
    if back_data:
        markup.row(types.InlineKeyboardButton(back_text, callback_data=back_data))

    return markup

# Helper function to check if user is in database
def check_user_in_db(user_id):
    conn = sqlite3.connect('Database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT status FROM Users WHERE userid = ?', (user_id,))
    result = cursor.fetchone()
    conn.close()
    return result

# Helper function to add user to database
def add_user_to_db(user_id, status=0):
    conn = sqlite3.connect('Database.db')
    cursor = conn.cursor()
    cursor.execute('INSERT OR IGNORE INTO Users (userid, status) VALUES (?, ?)', (user_id, status))
    conn.commit()
    conn.close()

# Helper function to update user status
def update_user_status(user_id, status):
    conn = sqlite3.connect('Database.db')
    cursor = conn.cursor()
    cursor.execute('UPDATE Users SET status = ? WHERE userid = ?', (status, user_id))
    conn.commit()
    conn.close()

# Start command
@bot.message_handler(commands=['start'])
def start(message):
    user_id = message.chat.id

    # Check if user exists in database
    user_status = check_user_in_db(user_id)

    # If user not in database, add them
    if user_status is None:
        add_user_to_db(user_id)

    markup = types.InlineKeyboardMarkup(row_width=2)
    markup.add(
        types.InlineKeyboardButton("🎥 افلام", callback_data="movies"),
        types.InlineKeyboardButton("🎬 المسلسلات", callback_data="series"),
        types.InlineKeyboardButton("💬 الدعم", url="https://t.me/EmoTech85")
    )

    # Show activation status
    user_status = check_user_in_db(user_id)
    welcome_text = f"""
🎉 مرحبًا بك في أكبر قناة كرتون في الوطن العربي!
📺 قناة "أطفال المستقبل المشرق"

نقدّم لك محتوى تعليمي، ترفيهي، ديني وهادف، موجه بعناية لأطفال العرب،
ومنتقى بعناية ليكون خاليًا تمامًا من أي مشاهد أو أفكار مخالفة لديننا وثقافتنا.

💳 الاشتراك مدى الحياة، مقابل مبلغ رمزي واحد فقط.

🚫 الدخول مخصص فقط للمشتركين
📩 للتفعيل: تواصل الآن مع الإدارة من هنا:
👉 اضغط هنا للتواصل مع الإدارة

{"حالة الحساب: مفعل ✅" if user_status and user_status[0] == 1 else "حالة الحساب: غير مفعل ❌"}
"""

    bot.send_message(message.chat.id, welcome_text, reply_markup=markup)

# Admin command handler
@bot.message_handler(commands=['admin'])
def admin_panel(message):
    user_id = message.from_user.id
    if user_id not in ADMIN_IDS:
        return

    markup = types.InlineKeyboardMarkup(row_width=2)
    btn1 = types.InlineKeyboardButton("تفعيل مستخدم", callback_data="admin_activate")
    btn2 = types.InlineKeyboardButton("رفع ملف Excel", callback_data="admin_excel")
    markup.add(btn1, btn2)
    bot.send_message(message.chat.id, "لوحة التحكم:", reply_markup=markup)

# Dictionary to store user states
user_states = {}

# Navigation data storage with indexes
nav_data = {
    'excel_files': [],      # List of Excel files
    'sheets': {},           # {excel_index: [sheet_names]}
    'series': {},           # {f"{excel_index}_{sheet_index}": [series_names]}
    'seasons': {},          # {f"{excel_index}_{sheet_index}_{series_index}": [seasons]}
    'episodes': {}          # {f"{excel_index}_{sheet_index}_{series_index}_{season}": episodes_data}
}

def refresh_nav_data():
    """Refresh navigation data with current Excel files"""
    global nav_data
    nav_data = {
        'excel_files': [],
        'sheets': {},
        'series': {},
        'seasons': {},
        'episodes': {}
    }

    # Get Excel files
    nav_data['excel_files'] = get_excel_files_in_series()

    # Pre-load sheets for each Excel file
    for excel_idx, excel_file in enumerate(nav_data['excel_files']):
        sheets = get_sheets_from_excel(excel_file)
        nav_data['sheets'][excel_idx] = sheets

        # Pre-load series for each sheet
        for sheet_idx, sheet_name in enumerate(sheets):
            key = f"{excel_idx}_{sheet_idx}"
            series_names = get_series_names_from_sheet(excel_file, sheet_name)
            nav_data['series'][key] = series_names

def get_excel_by_index(index):
    """Get Excel file name by index"""
    if 0 <= index < len(nav_data['excel_files']):
        return nav_data['excel_files'][index]
    return None

def get_sheet_by_index(excel_idx, sheet_idx):
    """Get sheet name by Excel and sheet index"""
    if excel_idx in nav_data['sheets'] and 0 <= sheet_idx < len(nav_data['sheets'][excel_idx]):
        return nav_data['sheets'][excel_idx][sheet_idx]
    return None

def get_series_by_index(excel_idx, sheet_idx, series_idx):
    """Get series name by indexes"""
    key = f"{excel_idx}_{sheet_idx}"
    if key in nav_data['series'] and 0 <= series_idx < len(nav_data['series'][key]):
        return nav_data['series'][key][series_idx]
    return None

# Initialize navigation data
refresh_nav_data()

# Callback handler
@bot.callback_query_handler(func=lambda call: True)
def callback_query(call):
    # Show all series (Excel files in series folder)
    if call.data == "series":
        # Check if user is activated
        user_id = call.from_user.id
        user_status = check_user_in_db(user_id)

        # If user not in database, add them
        if user_status is None:
            add_user_to_db(user_id)
            user_status = (0,)

        # If user is not activated, show message
        if user_status[0] == 0:
            bot.answer_callback_query(call.id, "حسابك غير مفعل. يرجى التواصل مع الإدارة للتفعيل.", show_alert=True)
            return

        # Refresh navigation data
        refresh_nav_data()

        if not nav_data['excel_files']:
            bot.answer_callback_query(call.id, "لا توجد ملفات في مجلد المسلسلات", show_alert=True)
            return

        buttons = [(file.replace('.xlsx', '').replace('.xls', ''), f"e_{idx}")
                  for idx, file in enumerate(nav_data['excel_files'])]
        markup = create_keyboard(buttons, back_data="main", back_text="🔙 القائمة الرئيسية")
        bot.edit_message_text("اختر الفئة الرئيسية:", call.message.chat.id, call.message.message_id, reply_markup=markup)

    # Show sheets (sub-categories) from selected Excel file
    elif call.data.startswith("e_"):
        excel_idx = int(call.data.split("_")[1])
        excel_file = get_excel_by_index(excel_idx)

        if not excel_file or excel_idx not in nav_data['sheets']:
            bot.answer_callback_query(call.id, "لا توجد أوراق في هذا الملف", show_alert=True)
            return

        sheets = nav_data['sheets'][excel_idx]
        buttons = [(sheet, f"s_{excel_idx}_{idx}") for idx, sheet in enumerate(sheets)]
        markup = create_keyboard(buttons, back_data="series", back_text="🔙 رجوع للفئات الرئيسية")
        bot.edit_message_text(f"اختر الفئة الفرعية من {excel_file.replace('.xlsx', '').replace('.xls', '')}:",
                             call.message.chat.id, call.message.message_id, reply_markup=markup)

    # Show series names from selected sheet
    elif call.data.startswith("s_"):
        parts = call.data.split("_")
        excel_idx = int(parts[1])
        sheet_idx = int(parts[2])

        key = f"{excel_idx}_{sheet_idx}"
        if key not in nav_data['series']:
            bot.answer_callback_query(call.id, "لا توجد مسلسلات في هذه الفئة", show_alert=True)
            return

        series_names = nav_data['series'][key]
        sheet_name = get_sheet_by_index(excel_idx, sheet_idx)

        buttons = [(name, f"n_{excel_idx}_{sheet_idx}_{idx}") for idx, name in enumerate(series_names)]
        markup = create_keyboard(buttons, back_data=f"e_{excel_idx}", back_text="🔙 رجوع للفئات الفرعية")
        bot.edit_message_text(f"اختر المسلسل من {sheet_name}:",
                             call.message.chat.id, call.message.message_id, reply_markup=markup)

    # Show seasons for selected series
    elif call.data.startswith("n_"):
        parts = call.data.split("_")
        excel_idx = int(parts[1])
        sheet_idx = int(parts[2])
        series_idx = int(parts[3])

        excel_file = get_excel_by_index(excel_idx)
        sheet_name = get_sheet_by_index(excel_idx, sheet_idx)
        series_name = get_series_by_index(excel_idx, sheet_idx, series_idx)

        if not all([excel_file, sheet_name, series_name]):
            bot.answer_callback_query(call.id, "خطأ في البيانات", show_alert=True)
            return

        seasons = get_seasons_from_series(excel_file, sheet_name, series_name)
        if not seasons:
            bot.answer_callback_query(call.id, "لا توجد مواسم لهذا المسلسل", show_alert=True)
            return

        buttons = [(f"موسم {season}", f"se_{excel_idx}_{sheet_idx}_{series_idx}_{season}") for season in seasons]
        markup = create_keyboard(buttons, back_data=f"s_{excel_idx}_{sheet_idx}", back_text="🔙 رجوع للمسلسلات")
        bot.edit_message_text(f"اختر الموسم من {series_name}:",
                             call.message.chat.id, call.message.message_id, reply_markup=markup)

    # Show episodes for selected season with pagination
    elif call.data.startswith("se_"):
        parts = call.data.split("_")
        excel_idx = int(parts[1])
        sheet_idx = int(parts[2])
        series_idx = int(parts[3])
        season = int(parts[4])

        excel_file = get_excel_by_index(excel_idx)
        sheet_name = get_sheet_by_index(excel_idx, sheet_idx)
        series_name = get_series_by_index(excel_idx, sheet_idx, series_idx)

        if not all([excel_file, sheet_name, series_name]):
            bot.answer_callback_query(call.id, "خطأ في البيانات", show_alert=True)
            return

        episodes_df = get_episodes_from_season(excel_file, sheet_name, series_name, season)
        if episodes_df.empty:
            bot.answer_callback_query(call.id, "لا توجد حلقات لهذا الموسم", show_alert=True)
            return

        buttons = [(f"حلقة {int(row['Episode'])}", f"ep_{excel_idx}_{sheet_idx}_{series_idx}_{season}_{int(row['Episode'])}")
                  for _, row in episodes_df.iterrows()]

        prefix = f"eps_{excel_idx}_{sheet_idx}_{series_idx}_{season}"
        markup = create_paginated_keyboard(buttons, page=0, prefix=prefix,
                                         back_data=f"n_{excel_idx}_{sheet_idx}_{series_idx}",
                                         back_text="🔙 رجوع للمواسم")
        bot.edit_message_text(f"اختر الحلقة من {series_name} موسم {season}:",
                             call.message.chat.id, call.message.message_id, reply_markup=markup)

    # Handle episode pagination
    elif "_prev_" in call.data or "_next_" in call.data:
        if "_prev_" in call.data:
            parts = call.data.split("_prev_")
            page = int(parts[1])
        else:
            parts = call.data.split("_next_")
            page = int(parts[1])

        prefix_parts = parts[0].split("_")
        if prefix_parts[0] == "eps":
            excel_idx = int(prefix_parts[1])
            sheet_idx = int(prefix_parts[2])
            series_idx = int(prefix_parts[3])
            season = int(prefix_parts[4])

            excel_file = get_excel_by_index(excel_idx)
            sheet_name = get_sheet_by_index(excel_idx, sheet_idx)
            series_name = get_series_by_index(excel_idx, sheet_idx, series_idx)

            if not all([excel_file, sheet_name, series_name]):
                bot.answer_callback_query(call.id, "خطأ في البيانات", show_alert=True)
                return

            episodes_df = get_episodes_from_season(excel_file, sheet_name, series_name, season)
            buttons = [(f"حلقة {int(row['Episode'])}", f"ep_{excel_idx}_{sheet_idx}_{series_idx}_{season}_{int(row['Episode'])}")
                      for _, row in episodes_df.iterrows()]

            prefix = f"eps_{excel_idx}_{sheet_idx}_{series_idx}_{season}"
            markup = create_paginated_keyboard(buttons, page=page, prefix=prefix,
                                             back_data=f"n_{excel_idx}_{sheet_idx}_{series_idx}",
                                             back_text="🔙 رجوع للمواسم")
            bot.edit_message_text(f"اختر الحلقة من {series_name} موسم {season}:",
                                 call.message.chat.id, call.message.message_id, reply_markup=markup)

    # Handle episode selection
    elif call.data.startswith("ep_"):
        parts = call.data.split("_")
        excel_idx = int(parts[1])
        sheet_idx = int(parts[2])
        series_idx = int(parts[3])
        season = int(parts[4])
        episode = int(parts[5])

        excel_file = get_excel_by_index(excel_idx)
        sheet_name = get_sheet_by_index(excel_idx, sheet_idx)
        series_name = get_series_by_index(excel_idx, sheet_idx, series_idx)

        if not all([excel_file, sheet_name, series_name]):
            bot.answer_callback_query(call.id, "خطأ في البيانات", show_alert=True)
            return

        try:
            file_path = os.path.join('series', excel_file)
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            row = df[(df['Name'] == series_name) & (df['Season'] == season) & (df['Episode'] == episode)].iloc[0]
            link = row['Link']

            # Get from_chat_id from Excel
            from_chat_id_excel = row['Channel ID'] if 'Channel ID' in row and not pd.isna(row['Channel ID']) else None

            if "t.me/c/" in str(link):
                parts = link.split("/")
                message_id = int(parts[5])

                # If from_chat_id found in Excel, use it, else extract from link
                if from_chat_id_excel:
                    from_chat_id = int(from_chat_id_excel)
                else:
                    chat_numeric_id = parts[4]
                    from_chat_id = int(f"-100{chat_numeric_id}")

                print(f"Forwarding message from chat_id: {from_chat_id}, message_id: {message_id}")
                bot.forward_message(chat_id=call.message.chat.id, from_chat_id=from_chat_id, message_id=message_id)
        except Exception as e:
            bot.answer_callback_query(call.id, f"خطأ في تحميل الحلقة: {str(e)}", show_alert=True)

    # Admin: Activate user
    elif call.data == "admin_activate":
        user_id = call.from_user.id
        if user_id not in ADMIN_IDS:
            return

        bot.edit_message_text("أدخل معرف المستخدم (User ID) الذي تريد تفعيله:", call.message.chat.id, call.message.message_id)
        user_states[user_id] = "waiting_for_userid"

    # Admin: Upload Excel
    elif call.data == "admin_excel":
        user_id = call.from_user.id
        if user_id not in ADMIN_IDS:
            return

        bot.edit_message_text("أرسل ملف Excel الجديد:", call.message.chat.id, call.message.message_id)
        user_states[user_id] = "waiting_for_excel"

    # Go back to main menu
    elif call.data == "main":
        start(call.message)

# Message handler for text messages (for admin functions)
@bot.message_handler(func=lambda message: message.from_user.id in user_states, content_types=['text'])
def handle_text_input(message):
    user_id = message.from_user.id
    state = user_states.get(user_id)

    # Admin is waiting for user ID to activate
    if state == "waiting_for_userid":
        try:
            target_user_id = int(message.text.strip())
            # Check if user exists in database
            user_status = check_user_in_db(target_user_id)

            if user_status is None:
                # User not in database, add them
                add_user_to_db(target_user_id, 1)
                bot.reply_to(message, f"تم إضافة المستخدم {target_user_id} وتفعيل حسابه.")
            else:
                # Update user status
                update_user_status(target_user_id, 1)
                bot.reply_to(message, f"تم تفعيل حساب المستخدم {target_user_id}.")
            
            msg = """
🎉 أهلًا وسهلًا بك معنا في قناة "أطفال المستقبل المشرق"!
نحن سعداء بانضمامك لعائلتنا ❤️

📺 هنا تجد لطفلك محتوى آمن، هادف، تعليمي، وترفيهي
ننتقي كل مسلسل وفيلم بعناية، ليناسب قيمنا الإسلامية والعربية.

📚 محتوى القناة مقسّم إلى
افلام ومسلسلات
الافلام مقسمه الى 3 تصنيفات رئيسية وهى (دينى وهادف , تعليمى وترفيهى , قصص ومغامرات)
 المسلسلات الى 7 تصنيفات رئيسية لتسهيل التصفح وهى
(ديني – قصص وحكايات – مغامرات – تعليمي – ترفيهي كوميدي – آداب وأخلاقيات – رياضة)

يمكنك رؤية محتوى القناة عبر الهاتف المحمول أو التابلت أو الكمبيوتر الشخصى من خلال حسابك

💡  لا تتردد في التواصل مع الإدارة لأي استفسار أو اقتراح او الابلاغ عن اى محتوى غير مناسب.

🚀 نتمنى لك ولأطفالك مشاهدة ممتعة ومفيدة دائمًا!"
"""

            bot.send_message(target_user_id, msg)
            
        except ValueError:
            bot.reply_to(message, "الرجاء إدخال رقم صحيح.")

        # Clear user state
        del user_states[user_id]

# Message handler for document uploads (Excel files)
@bot.message_handler(func=lambda message: message.from_user.id in user_states, content_types=['document'])
def handle_document(message):
    user_id = message.from_user.id
    state = user_states.get(user_id)

    # Admin is waiting for Excel file
    if state == "waiting_for_excel":
        # Check if document is an Excel file
        if message.document.mime_type in ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']:
            try:
                # Download file
                file_info = bot.get_file(message.document.file_id)
                downloaded_file = bot.download_file(file_info.file_path)

                # Save file
                with open('Data.xlsx', 'wb') as new_file:
                    new_file.write(downloaded_file)

                # Reload Excel data
                global xls
                xls = pd.ExcelFile('Data.xlsx')

                bot.reply_to(message, "تم استلام وحفظ ملف Excel بنجاح.")
            except Exception as e:
                bot.reply_to(message, f"حدث خطأ أثناء معالجة الملف: {str(e)}")
        else:
            bot.reply_to(message, "الرجاء إرسال ملف Excel فقط.")

        # Clear user state
        del user_states[user_id]

# Polling
bot.infinity_polling()
